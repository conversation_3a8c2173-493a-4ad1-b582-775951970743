zh-CN:
  activerecord:
    models:
      admin: "管理员"
      agent: "代理商"
      channel: "渠道"
      promotion_type: "推广类型"
      role: "角色"
      menu: "菜单"
      payment: "支付通道"
      payment_type: "支付类型"
      game_type: "游戏类型"
      hall_ad: "大厅广告"
      popup_ad: "弹窗广告"
      floating_ad: "悬浮入口广告"
      integrator: "集成商"
      black_ip: "IP黑名单"
      black_bank_card: "银行卡黑名单"
      blacklist_record: "用户黑名单"
    attributes:
      admin:
        name: "账号"
        nickname: "昵称"
        old_password: "原密码"
        password: "新密码"
        password_confirmation: "确认密码"
        status: "状态"
        statuses:
          actived: "正常"
          locked: "已锁定"
          deleted: "已删除"
      agent:
        name: "账号"
        nickname: "昵称"
        password: "密码"
        old_password: "原密码"
        status: "状态"
        statuses:
          actived: "正常"
          inactived: "已禁用"
          deleted: "已删除"
      channel:
        name: "渠道名称"
        agent: "代理"
        promotion_type: "推广类型"
        password: "密码"
        nickname: "昵称"
        email_address: "邮箱"
        mobile: "手机号"
        remark: "备注"
        status: "状态"
      promotion_type:
        name: "活动类型"
        code: "活动代码"
        sort: "排序"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
      black_ip:
        ip: "IP地址"
        remark: "备注"
      black_bank_card:
        card_number: "卡号"
        remark: "备注"
      blacklist_record:
        user_id: "用户ID"
        ban_type: "黑名单类型"
        reason: "黑名单原因"
        actived: "是否生效"
        expires_at: "自动解封时间"
        created_by: "操作人"
      floating_ad:
        name: "广告名称"
        media: "广告图片"
        media_type: "媒体类型"
        type: "广告类型"
        ad_position: "广告位置"
        ad_positions:
          left: "左侧"
          right: "右侧"
        redirect_type: "跳转类型"
        redirect_types:
          no_redirect: "不跳转"
          internal: "站内"
          external: "站外"
          popup: "弹窗"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"  
        
      hall_ad:
        name: "广告名称"
        media: "广告图片"
        media_type: "媒体类型"
        type: "广告类型"
        ad_position: "广告位置"
        redirect_type: "跳转类型"
        redirect_link: "跳转链接"
        display_occasion: "展示场合/时机"
        display_platform: "展示平台"
        visibility: "可见性"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
        ad_position: "广告位置"
        ad_positions:
          hall_slider: "大厅轮播"
          hall_big_kinggang: "大厅大金刚"
          hall_mini_kinggang: "大厅小金刚"
          hall_top_nav: "大厅顶部导航"
          sidebar_big_kinggang: "侧边大金刚"
          sidebar_mini_kinggang: "侧边小金刚"
          hall_bottom: "大厅底部"
        redirect_type: "跳转类型"
        redirect_types:
          no_redirect: "不跳转"
          internal: "站内"
          external: "站外"
          popup: "弹窗"
      popup_ad:
        name: "广告名称"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
        redirect_type: "跳转类型"
        redirect_types:
          no_redirect: "不跳转"
          internal: "站内"
          external: "站外"
          popup: "弹窗"
        display_occasion: "展示时机"
        display_occasions:
          daily_first_login: "每日首次登录"
          every_refresh: "每次刷新"
        visibility: "可见性"
        visibilities:
          all_users: "全体"
          un_paid: "未充值"
          paid: "已充值"
      role:
        status: "状态"
        statuses:
          actived: "正常"
          inactived: "已禁用"
      game_type:
        name: "类型名称"
        order: "排序"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
      game_platform:
        name: "平台名称"
        order: "排序"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
      game:
        name: "游戏名称"
        game_platform: "游戏平台"
        integrator: "集成商"
        game_type: "游戏类型"
        screen_direction: "游戏方向"
        screen_directions:
          auto: "自动"
          portrait: "竖屏"
          landscape: "横屏"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
      integrator:
        name: "集成商名称"
        order: "排序"
        status: "状态"
        statuses:
          onlined: "上线"
          offlined: "下线"
      payment_type:
        name: "名称"
        weight: "权重"
        status: "状态"
        statuses:
          actived: "正常"
          inactived: "已禁用"
      payment:
        status: "状态"
        statuses:
          actived: "正常"
          inactived: "已禁用"
      tag:
        status: "状态"
        statuses:
          actived: "启用"
          inactived: "禁用"
      user:
        layers:
          reg_30m_unpay: "注册30分钟未充值"
          reg_2h_unpay: "注册2小时未充值"
          reg_curday_unpay: "注册当日未充值"
          first_nextday_unlogin: "首充次日未登录"
          first_3d_unlogin: "首充3日未登录"
          first_7d_unlogin: "首充7日未登录"
          first_15d_unlogin: "首充15日未登录"
          first_30d_unlogin: "首充30日未登录"
          first_curday_win: "首充当日赢钱用户"
          first_curday_loss: "首充当日亏损用户"
          pay_15d_unlogin: "充值用户-15天未登录"
    errors:
      models:
        admin:
          attributes:
            password:
              too_short: "密码太短（至少需要 %{count} 个字符）"
            password_confirmation:
              confirmation: "两次输入的密码不一致"
        game_type:
          attributes:
            name:
              blank: "类型名称不能为空"
              taken: "类型名称已被使用"
            order:
              blank: "排序不能为空"
              not_a_number: "排序必须是数字"
            status:
              blank: "状态不能为空"
        integrator:
          attributes:
            name:
              blank: "集成商名称不能为空"
              taken: "集成商名称已被使用"
            order:
              blank: "排序不能为空"
              not_a_number: "排序必须是数字"
            status:
              blank: "状态不能为空"